# CIRCUIT — Coach‑led • AI‑assisted

A modern remote personal‑training platform where **real coaches** lead and **smart tools** accelerate progress. This document is the single‑source overview for product, design, data, and engineering.

---

## 1) Value Proposition (Plain English)
- **You get a real coach**, not just an app.
- **Workouts built for you** change as your body and schedule change.
- **Simple tests** show where you’re starting and what to fix.
- **Clear daily plan** + form tips + check‑ins = steady progress without overthinking.

**One‑liner:** *“Real coaching. Smarter reps.”*

---

## 2) Who It’s For
- Beginners who want guidance without the gym‑floor anxiety.
- Busy people who need efficient, equipment‑aware plans at home or gym.
- Intermediate/advanced lifters who want structure, form feedback, and PR targeting.
- Coaches who need a clean portal to manage clients and scale quality.

---

## 3) Brand & Visual System
- **Primary color:** Scarlet `#FF1F3D` → Vermilion `#FF4D4D` (glow gradient)
- **Dark background (default):** Midnight Charcoal `#0B0F14`
- **Text on dark:** White `#F3F6FA`, Subtext `#A5ADB9`
- **Neutrals:** Card `#121A2B`, Lines `#232B36`
- **Accent (sparingly):** Neo‑Lime `#B8F70A` for primary CTA
- **Logo:** Concentric **C** with neon scarlet halo; wordmark in white; tagline: *Coach‑led • AI‑assisted*
- **Tone:** Confident, direct, helpful. No jargon.

---

## 4) Core Jobs‑to‑Be‑Done
1) **“Tell me what to do today.”**
2) **“Make sure I’m doing it right and not getting hurt.”**
3) **“Adjust when life happens.”**
4) **“Show me that I’m improving.”**
5) **“Let me reach my coach easily.”**

---

## 5) Key Features (Product Level)
### 5.1 Smart Workout Generation
- Day‑by‑day plans matched to goal, equipment, schedule, and history.
- Auto‑progression (reps, load, tempo) with simple rules.
- Easy **exercise swap** with safe alternates (same muscle/pattern).

### 5.2 Progress Prediction
- Forecasts PRs, bodyweight/pace trends using simple, robust models.
- “On‑track meter” for each goal with a clear date target.

### 5.3 Goal Optimization
- Weekly tune‑ups that reallocate sets, tweak RIR/tempo, or add cardio minutes.
- One‑tap approve/undo with a short reason: “+2 hamstrings sets; −1 quads; add 15’ Zone 2 Wed.”

### 5.4 Performance Analytics
- Three sections: **Progress**, **Inputs**, **Risks** (green/amber/red).
- Only actionable charts. Every chart ends with “Do next: …”.

### 5.5 Form Analysis (Optional)
- Phone camera checks depth, range, and common faults.
- Real‑time cues: “Keep heels down.” “Elbows tuck 15°.”
- On‑device by default; clips saved only with consent.

### 5.6 Adaptive Training
- Adjusts today’s session using readiness (sleep/HRV/soreness), warm‑up taps (e.g., jump height), and in‑set RPE/velocity.
- Safety first: lowers volume when red flags appear; schedules deloads automatically.

### 5.7 Coach Portal
- Client roster, chat/video, program templates, bulk edits.
- Side‑by‑side video review with timestamped comments and cue library.
- Alerts: low adherence, soreness spikes, risk flags.

### 5.8 Community & Accountability
- Streaks, weekly challenges, and small team groups (opt‑in).
- Private by default; no public leaderboards.

### 5.9 Payments & Plans
- Free onboarding + 7‑day sample plan.
- Monthly app (self‑guided) vs **Coach‑led** tiers (1×/week or 2×/week check‑ins).

---

## 6) First‑Run Flow (Beginner Friendly)
1) **Create account → pick goal** (feel better / lose fat / get stronger / run faster).
2) **Equipment & schedule** (home/gym, days, minutes per session).
3) **Baseline tests** (10–12 minutes total):
   - **Chair Squat 30s** (reps) → band: Starter/Building/Strong/Athletic
   - **Push‑Up 60s** (pick the hardest surface you can do 5+) → reps & version
   - **Core**: 1‑min Crunch *or* Plank hold (max 120s)
4) **Injuries or limits?** (simple toggles: knees, shoulders, back, impact)
5) **Today’s Plan** shown immediately with clear steps and rest timers.
6) **Optional camera form check** for one key move.
7) Post‑session summary + next session preview + nudge time.

---

## 7) Daily Session UX
- **Top bar:** readiness badge (green/amber/red), ETA, swap button.
- **Block A:** main lift card (target reps, RIR, rest). Tap → tips + demo.
- **Block B:** accessories (superset labels if any).
- **Finisher** (short cardio/core/conditioning) when time allows.
- **In‑set controls:** +/− weight, rep counter, RPE slider, timer auto‑starts on “Complete set”.
- **Instant feedback:** “Hit rep target twice → add 2.5–5% next time.”

---

## 8) Data & Decisions (How it actually works)
- **Inputs:** training logs, set RPE, optional HR/HRV/sleep/steps, soreness map, short notes.
- **Planner:** rules + constraints (weekly set targets, intensity bands, time budget, injury rules).
- **Forecaster:** Kalman/ETS smoothing for e1RM/bodyweight/pace; probability of hitting target by date.
- **Optimizer:** small constrained solver that shifts weekly sets/tempo/RIR within safe bounds.
- **Form engine:** on‑device pose; rep/ROM/tempo; flags common faults with confidence.

**Guardrails:** Max +10–20% weekly volume change; deload 4–6 weeks or when readiness stays low; respect injury toggles; no claims of medical diagnosis.

---

## 9) Minimal Data Model (Starter)
- **User** {id, profile, equipment[], schedule, injury_flags[]}
- **Plan** {week_id, days[ {blocks[ {exercise_id, sets, reps, rir, tempo, rest_s} ]} ]}
- **Log** {date, exercise_id, set, reps, load, rpe, notes, video_uri?}
- **Readiness** {date, sleep_h, hrv, soreness_map, steps}
- **Forecast** {metric, current, expected_on_date, p_goal_by_date}
- **CoachNote** {user_id, timestamp, text, markers[]}

---

## 10) API Sketch (Developer Contract)
- `POST /plan/generate` → create or refresh week plan from profile & history
- `POST /session/adapt` → return today’s tweaks from readiness + live data
- `POST /progress/forecast` → probabilities & targets
- `GET  /analytics/overview?window=28d` → cards for Progress/Inputs/Risks
- `POST /form/analyze` → reps, ROM, faults, cues (on‑device preferred)
- `POST /coach/note` and `GET /coach/alerts`

All endpoints return **plain words** alongside numbers (e.g., “Add 2.5–5% next bench session”).

---

## 11) Tech Stack (Pragmatic)
- **Mobile:** React Native + Expo; TypeScript; offline‑first; background timers; media capture.
- **Backend:** FastAPI (Python) + Postgres (RDS) + Redis; object storage for videos (S3/GCS). Workers (RQ/Celery) for analytics.
- **ML:** TensorFlow Lite / MediaPipe for pose; scikit‑learn/statsmodels for forecasting; on‑device first.
- **Integrations:** Apple Health, Google Fit; optional wearables later.
- **Infra:** Terraform/IaC; feature flags; blue‑green deploys.

---

## 12) Security & Privacy
- Video analysis on device by default; metrics only unless user opts to upload.
- At‑rest encryption for logs/videos; in‑transit TLS 1.2+.
- Data export & delete‑my‑data in settings; clear consent screens; HIPAA‑aware if needed.

---

## 13) Habits, Nudges, and Retention
- **Daily nudge** at chosen time: start session or short “win of the day.”
- **After action prompts:** 2‑tap reflection: “How did it feel?” → adapts next time.
- **Weekly recap** with 1 micro‑goal (e.g., “Add 10 total minutes of Zone 2”).
- **Streaks:** gentle; pauses for illness/injury so users aren’t punished.

---

## 14) Accessibility & Inclusivity
- Text size controls; color‑contrast AA+.
- Voice cues for timers and form tips.
- Low‑impact options for joint issues; clear alternatives for every move.

---

## 15) Pricing (Example)
- **App‑only:** $14.99/month (smart plans + analytics).
- **Coach‑Lite:** $59/month (1×/week feedback, chat).
- **Coach‑Plus:** $149/month (2×/week video review + custom blocks).
- Corporate/team bundles later.

---

## 16) Success Metrics (North Stars)
- D30 **retention ≥ 40%** (coach‑led ≥ 60%).
- Weekly **completion rate ≥ 75%** of planned sets.
- Average **time‑to‑first‑result** (move up one band or +5 clean reps) ≤ 14 days.
- **Coach capacity**: ≥ 40 active clients with SLA met.

---

## 17) Risks & Mitigations
- **Over‑automation** → Keep coach approval and explain‑why.
- **Camera friction** → Make form check optional & quick; auto‑suggest once/week.
- **Data gaps** → Robust defaults; ask 2–3 questions instead of 20.
- **Injury concerns** → Clear stop rules; conservative progressions; report button to coach.

---

## 18) Roadmap
**V1 (8–10 weeks)**
- Onboarding, baseline tests, weekly plan, swaps, timers, logs.
- Simple forecasts, weekly tune‑ups, readiness badge.
- Coach portal: roster, chat, manual edits.

**V1.5**
- Form check for 4 moves (squat, hinge, push‑up, row).
- App‑only plan, payments, referrals.

**V2**
- Advanced forecasting (goal‑by‑date optimization), community teams, more device data, template marketplace for coaches.

---

## 19) Copy Blocks (Ready to ship)
- **Start screen:** “You bring the effort. We handle the plan.”
- **Test intro:** “Quick baseline—about 10 minutes. No gym needed.”
- **Session header:** “Today’s plan—about 42 minutes.”
- **Post‑session:** “Nice work. Next time we’ll add 2.5% to your dumbbell press.”
- **Risk flag:** “You’ve pushed hard three days straight. We trimmed one set to protect recovery.”

---

## 20) Acceptance Criteria (V1)
- New user to first workout in **≤ 5 minutes**.
- Any exercise has ≥ 3 alternates that match pattern/equipment.
- Week‑over‑week volume never jumps >20%.
- Baseline retest scheduled automatically at week 4.
- All critical actions possible **one‑handed** on phone.

---

## 21) Deliverables Check‑List
- SVG/PNG logo (dark/light), app icon, splash.
- Tailwind/React‑Native style tokens for COLORS/TYPO.
- OpenAPI 3.1 spec for endpoints above.
- Seed exercise library (150 moves) with tags: muscle, pattern, equipment, impact.
- Coach cue library (100 short cues) mapped to common faults.

---

**This document is designed to be edited as we build.** Tell me what to expand next: screens, OpenAPI spec, or the coach portal workflow.



---

## 8) Data & Decisions (Expanded)
This is the brain of CIRCUIT. Every decision follows a simple rule: **clear inputs → deterministic logic → human override**.

### 8.1 Inputs we track (minimal but useful)
- **Training logs**: exercise, sets, reps, RPE/RIR, load, rest, notes, optional clip.
- **Readiness**: sleep hours, optional HRV/resting HR (Apple/Google), soreness map (0–3), steps.
- **Schedule**: preferred days/times, session minutes, travel weeks.
- **Constraints**: equipment list, injury toggles (knees/shoulders/back/impact), space limits.
- **Goal spec**: target metric + date (e.g., “Bench 225 by Dec 15”).

### 8.2 Planner (how a week is built)
1) **Pick template** based on days/week: 3d full‑body, 4d upper/lower, 5d hybrid.
2) **Set weekly volume** per pattern/muscle (start ranges):
   - New lifter: 6–8 sets / week / pattern
   - Intermediate: 8–12
   - Advanced: 10–16 (cap 18 unless coached)
3) **Choose intensities**: target RIR 2 (hypertrophy), RIR 3 (new lifter), RIR 1–2 (strength blocks). Cardio zones: mostly **Zone 2** + optional intervals.
4) **Select moves** from equipment‑safe pool in this order: pattern → joint‑friendly → user preference → variety.
5) **Schedule** to fit session minutes; use supersets to save time.
6) **Auto‑progression**: if top set hits rep cap twice with RIR ≤ target → +2.5–5% next time; otherwise keep load and chase reps.

### 8.3 Forecaster (what we expect to happen)
- Smooth noisy metrics with **Kalman** / **ETS**.
- Features: compliance %, weekly volume, readiness avg, ACWR, sleep avg.
- Output: expected metric on goal date + **P(goal by date)**.
- Rule: if ACWR > 1.5 or readiness trend down 5+ days → soften forecast.

### 8.4 Optimizer (weekly tune‑ups)
- Objective: **max P(goal)** within safe bounds.
- Levers: ± sets (≤10% change), ± RIR (±0.5), swap to higher stimulus:fatigue moves, add 10–20 min Zone 2.
- Always explain change in plain English.

### 8.5 Safety & guardrails
- Volume growth capped at **≤20%/week**.
- Deload every 4–6 weeks **or** when readiness <40 for 3 days.
- Injury toggles block risky moves (e.g., deep knee flexion → prefer box squats, split squats).

---

## 9) Nutrition Engine (Detailed)
**Goal:** generate realistic meal plans that hit calories/macros using foods the client actually likes.

### 9.1 How we set targets
- **Calories (TDEE)**: Mifflin‑St Jeor × activity factor.
- **Deficit/Surplus**: fat loss −10–20%; gain +5–12%.
- **Macros**: Protein 1.8–2.2 g/kg (fat loss toward 2.2), Fat ≥0.6 g/kg, Carbs = remainder.

### 9.2 Personalization knobs
- Meals/day (3–5), cuisine, dislikes/allergies, prep time, budget, kitchen gear (microwave only?).
- Auto‑swaps for protein sources (chicken ↔ turkey ↔ salmon ↔ tofu/tempeh), grains, veg.

### 9.3 Output
- 7‑day plan or rolling 3‑day carousel, each with **recipes, quantities, macros**, and **two swaps** per meal.
- **Grocery list**: grouped by category; metric + imperial; smart rounding.
- **Adherence check** after day 3: “too hungry/too full?” → adjust ±100–200 kcal.

### 9.4 Guardrails
- Never include allergens or disliked foods.
- Hit calories within ±5%, protein within ±10g, fat within ±5g/day.
- Hydration reminder + simple fiber target (≥25–35g/day).

---

## 10) Form Analysis (Detailed)
- **On‑device** pose estimation (MoveNet/BlazePose). No upload unless user opts in.
- Rep detection via velocity + joint angle cycles; compute ROM %, tempo, symmetry.
- Fault flags mapped to short cues: e.g., **squat** (heels rise, knees cave, depth short), **hinge** (lumbar flex), **push‑up** (hips sag, flared elbows).
- Confidence score; if low, ask for better camera angle/lighting.

**Latency targets:** <120 ms real‑time cue, or <3 s/set in batch.

---

## 11) Coach Portal (Workflow)
1) **Inbox**: ranked alerts (low adherence, soreness spikes, risk flags, PRs).
2) **Client view**: last 7 days, next week plan, quick edits (drag to move sets/blocks), chat/video.
3) **Video review**: side‑by‑side clips, timestamped comments, insert canned cues.
4) **Templates**: save blocks or full weeks; apply to many clients with auto‑scaling.
5) **SLA**: Coach‑Lite replies ≤24h; Coach‑Plus ≤12h.

---

## 12) Chatbot (CIRCUIT Coach)
- Answers in plain English; always lists next steps.
- Pulls from curated knowledge (ACSM/WHO/NIH + house playbooks) via RAG.
- Tools: macro/workout generators, injury modifier, readiness adapt, grocery builder.
- **Escalation**: red‑flag symptoms → instruct to stop and message coach; open handoff ticket.
- **Memory**: stores preferences (likes/dislikes, equipment), not sensitive health notes unless user consents.

---

## 13) Telemetry & KPIs (What we measure)
- Time‑to‑first‑workout (goal: ≤5 min).
- Weekly completion rate (% of planned sets done).
- Diet adherence (days logged, kcal drift).
- D7/D30 retention; messages answered within SLA; PR count; injury flags.
- A/B gates tied to **on‑track** percentage.

**Core dashboards**: Progress (per user), Coach efficiency, Risk heatmap, Growth funnel.

---

## 14) API (Expanded Examples)
```http
POST /plan/generate
{
  "user_id":"123",
  "goal":"hypertrophy",
  "days_per_week":4,
  "session_minutes":50,
  "equipment":["dumbbells","bench","bands"],
  "injuries":["left_shoulder"],
  "history_weeks":6
}

POST /nutrition/plan
{
  "user_id":"123",
  "meals_per_day":4,
  "preferences":["mexican","american"],
  "likes":["chicken","rice","berries"],
  "dislikes":["eggs"],
  "allergies":["peanuts"],
  "budget":"medium"
}

POST /form/analyze
{ "user_id":"123", "exercise":"barbell_back_squat", "video_uri":"local://clip45.mp4" }

POST /session/adapt
{ "user_id":"123", "readiness":{"hrv_z":-0.6,"sleep":6.0,"soreness":2}, "live":{"last_top_set":{"rpe":8}} }
```

---

## 15) Security & Privacy (Expanded)
- On‑device first; encrypted at rest; TLS in transit; role‑based access for coaches.
- Clear consent for video uploads and data sharing; easy **export/delete**.
- PII minimization; separate auth store; audit logs for coach actions.

---

## 16) Edge Cases & Fallbacks
- **No equipment**: bodyweight templates with bands optional.
- **Travel week**: 20–25‑min sessions; hotel‑room moves.
- **Injury spike**: auto‑reduce volume, swap patterns, notify coach.
- **Data gaps**: conservative defaults; ask 1–2 clarifying questions, not a questionnaire.

---

## 17) Testing & Quality Gates
- Unit tests for macro math, plan growth caps, injury filters, swap safety.
- Golden users for end‑to‑end; reject plans with >20% volume jump.
- Pose tests on synthetic clips; require ≥85% fault‑flag precision before enabling auto‑cues.
- Load: 1k concurrent users; p95 API <300 ms (no video).

---

## 18) Roadmap (Milestones)
- **V1 (8–10 weeks):** onboarding + baseline tests, weekly plans, swaps, timers, nutrition v1, coach portal MVP.
- **V1.5:** form check for 4 moves, payments, referrals, team challenges.
- **V2:** advanced optimizer, device integrations, marketplace templates, deeper RAG.

---

## 19) Copy (Ready to paste)
- **Onboarding title:** “You bring the effort. We handle the plan.”
- **Baseline intro:** “Quick baseline—about 10 minutes. No gym needed.”
- **Daily header:** “Today’s plan—about {{minutes}} minutes.”
- **After set:** “Hit the top reps twice? Add 2.5–5% next time.”
- **Weekly tune‑up:** “We shifted a little work to hamstrings and added 15 minutes of easy cardio.”

---

## 20) Deliverables
- SVG/PNG logos + app icon; Tailwind/React‑Native tokens; OpenAPI spec; seed exercise & recipe libraries; cue library; coach template pack.



---

# End‑to‑End Program Plan (Build‑Ready)

This is the full outline from **first commit → launch**. It’s tailored for Augment AI/Replit, with deterministic logic, coach approval gates, and minimal moving parts.

## 1) Product North Star
- **Coach‑led, AI‑assisted**. Human decisions where it matters; AI for speed, personalization, and scheduling.
- **Four pillars:** Onboarding (with consent + screening), Plans (workout + diet), Approvals (trainer→client), Tracking (time, reps, RPE → Effort Score) + Progress Forecast.

## 2) MVP Scope (no bloat)
- **Onboarding**: questionnaire, optional photo, consent.
- **Screening**: 5 short videos → ROM/asymmetry/rep‑quality metrics (no medical claims).
- **Plan generation**: AI workout + AI diet (with recipe photos). Trainer review → client approve.
- **Session tracking**: active time, total reps, avg RPE → Effort Score + Recovery Hours.
- **Progress**: simple goal probability with “do next”.
- **Video library**: embedded via Cloudflare Stream/Mux/YouTube.
- **Coach portal**: approve queue, roster, quick edits, screening report review.

## 3) Architecture (Replit‑friendly)
- **Frontend**: Next.js (TS) + Tailwind. Minimal state (Zustand).
- **Backend**: FastAPI + Pydantic + SQLAlchemy (Postgres/Supabase).
- **Workers**: RQ/Celery for forecasts & heavy jobs.
- **Storage**: Supabase Storage/S3 for recipe photos; Cloudflare Stream/Mux for videos.
- **Auth**: `fastapi-users` (JWT). RBAC: client/coach/admin.
- **Observability**: structured JSON logs; Sentry optional.

```
web (Next.js) ──> api (FastAPI) ──> Postgres
                        │
                        ├─> Storage (S3/Supabase)
                        ├─> Video (Cloudflare/Mux/YT)
                        └─> Worker (RQ/Celery)
```

## 4) Data Model (tables)
- **users**(id, email, role, photo_url, created_at)
- **consents**(id, user_id, waiver_version, accepted_at, ip, ua)
- **assessments**(id, user_id, goal_metric/target/date, schedule, equipment, injuries, diet)
- **workouts**(id, user_id, week_start, plan_json, status: proposed|approved|rejected)
- **workout_logs**(id, user_id, workout_id, date, active_seconds, total_reps, avg_rpe, detail_json, effort_score, recovery_hours)
- **recipes**(id, title, calories, p_g, f_g, c_g, photo_url, source_url, tags[])
- **recipe_likes**(user_id, recipe_id, liked)
- **diet_plans**(id, user_id, start_date, days_json)
- **videos**(id, title, provider, provider_id, playback_url, thumb_url)
- **forecasts**(id, user_id, metric, goal_target, goal_date, latest_value, projected_value, probability, details)
- **screening_reports**(id, user_id, quality, tests_json, plain_english[], coach_notes[], waiver_version, created_at)
- **media_assets**(id, user_id, kind: photo|screening_video|workout_clip, provider, provider_id, playback_url, thumb_url, quality_score)

## 5) API Surface (contract outline)
- **Auth**: `POST /auth/register`, `POST /auth/login`
- **Consent & Media**: `POST /consent`, `POST /media/presign`, `POST /media/confirm`
- **Assessment**: `POST /assessments`, `GET /assessments/:id`
- **Screening**: `POST /screening/photo/analyze`, `POST /screening/video/analyze`, `POST /screening/report/:id/publish`, `GET /screening/report/:id`
- **Workouts**: `POST /workouts/generate`, `POST /workouts/:id/approve`, `POST /workouts/:id/reject`, `GET /workouts/current`
- **Workout Logs**: `POST /workout/logs`, `GET /progress/summary`
- **Diet**: `POST /diet/plan`, `POST /recipes/:id/like`, `GET /recipes/:id`
- **Videos**: `GET /videos/library`, `POST /videos/upload_url`

_All endpoints return human‑readable summaries alongside data._

## 6) Algorithms (deterministic, explainable)
### 6.1 Workout Generator
- Choose template by days/week (3‑day full‑body / 4‑day UL). Start volume 6–10 sets/pattern/wk.
- Equipment + injury filters choose safe moves. RIR targets: 2–3 (novice), 1–2 (strength blocks).
- **Progression**: top‑rep twice @ RPE≤8 → +1 set or +2.5–5% next week; cap volume growth ≤20%.

### 6.2 Diet Generator
- TDEE → deficit/surplus (−15% / +10%). Protein 1.8–2.2 g/kg; Fat ≥0.6 g/kg; Carbs = rest.
- Filter by likes/dislikes/allergens/cuisine/budget; 3–5 meals/day; every recipe has **photo_url**.
- Grocery list; 2 swaps/meal; adherence check (±100–200 kcal if hungry/full).

### 6.3 Screening Metrics
- 5 short videos → ROM %, asymmetry %, rep count, depth/line deviations; confidence score.
- Rules map metrics → plan constraints (e.g., heel‑elevated squats if ankle asymmetry >25%).

### 6.4 Effort Score & Recovery (stored on each log)
```
density = total_reps / (active_seconds/60)
density_index = clamp(density / baseline_density, 0..1.5)
intensity_index = clamp(avg_rpe / 8.0, 0..1.25)
score = min(round(60*density_index + 40*intensity_index), 100)
recovery_hours = clamp(16 + 8*(avg_rpe/10) + 6*(density_index-1), 12..36)
```

### 6.5 Goal Probability (weekly)
- Linear trend on last 6–8 weeks of the metric (e1RM/bodyweight/pace) + compliance factor; normal approx → P(hit by date) + “do next”.

## 7) UX Flows (screens)
1) **Create account → Consent** (4 checkboxes + sign) → optional photo upload.
2) **Questionnaire** (goal/date, days, minutes, equipment, injuries, diet likes/dislikes/allergens).
3) **Screening** (5 clips with live capture prompts) → instant quality check → report.
4) **Proposed Plans** page → Trainer edits → **Client Approves** (hard gate).
5) **Today’s Session** (sets/reps/RIR/timer, swaps). After log → Effort Score + Recovery hours.
6) **Diet** (weekly view with recipe photos, swaps, like/dislike, grocery list).
7) **Progress** (goal probability bar, trends, “do next”).
8) **Videos** (library, searchable; modal player). 
9) **Coach Portal** (approve queue, screening report review, roster, quick edits, chat).

## 8) Chatbot (CIRCUIT Coach)
- **System rules**: plain English; ask for missing essentials; safety guardrails; escalate red‑flags.
- **Tools**: workout_gen, diet_plan, injury_adjust, readiness_adapt, qa_retrieve, coach_handoff.
- **RAG**: curated corpus (guidelines + house playbooks). Cite source names in long answers.

## 9) Security, Privacy, Compliance
- On‑device analysis preferred; upload requires consent. TLS in transit; encryption at rest.
- Role‑based access; audit trails for coach actions; media retention 180 days (auto‑delete).
- PII minimization; delete/export endpoints; “not medical advice” badge on screening reports.

## 10) DevOps & Environments
- **Secrets** via Replit/Env vars: DATABASE_URL, JWT_SECRET, STORAGE_*, STREAM_*.
- **Migrations** with Alembic. **Feature flags** for screening + chatbot. 
- **CI checks**: lint, type, unit tests, smoke tests (Postman collection). 

## 11) Acceptance Criteria (MVP)
- A plan cannot start until **client approves** (status flips to `approved`).
- Diet plan never includes **dislikes/allergens**; every recipe has a **photo_url**.
- Logging a session stores **Effort Score** and **Recovery Hours**.
- Screening reports show numeric metrics + confidence; no medical claims.
- Weekly forecast renders **probability** + one actionable suggestion.
- Videos play in‑app from at least one provider.

## 12) Folder Structure (monorepo)
```
apps/
  web/ (Next.js)
    pages/ onboarding, plan, workout/[date], diet, progress, videos, coach
    components/ cards, timers, charts, approvals
    lib/ api.ts, auth.ts
  api/ (FastAPI)
    app/ main.py, deps.py
    routers/ auth.py, consent.py, media.py, assessments.py, screening.py,
            workouts.py, logs.py, diet.py, videos.py, progress.py
    services/ workout_gen.py, diet_gen.py, effort.py, forecast.py, video_provider.py
    models/ (SQLAlchemy)  schemas/ (Pydantic)  db.py
    tests/
infra/
  docker/, alembic/, scripts/
```

## 13) Content Pack (launch‑ready)
- **Exercises** (150) with tags: pattern, muscle, equipment, joint‑stress; GIF/MP4 links.
- **Recipes** (200) with macros + **photo_url** + tags (cuisine, prep time, allergens). 
- **Cues** (100) mapped to common faults.

## 14) Timeline (6‑week aggressive MVP)
- **Week 1**: Auth, Consent, Assessment, Media presign; DB + basic screens.
- **Week 2**: Workout/Diet generators (deterministic); Proposed→Approve flow.
- **Week 3**: Session Player + Logs + Effort Score; Videos library.
- **Week 4**: Screening capture + analyzer (benchmarks on 5 tests) + Report.
- **Week 5**: Progress page (probability), Coach portal (approve queue), Recipe likes.
- **Week 6**: Polish, edge cases, CI, seed content, pilot onboarding.

## 15) Risks & Mitigations
- **Media privacy** → strict consent, retention, on‑device default.
- **Model drift/over‑promises** → deterministic first, simple forecasts, clear copy.
- **Adherence drop** → travel mode, smart swaps, streaks without shame.
- **Coach workload** → approval queue, templates, diff view for weekly tune‑ups.

## 16) Launch Checklist
- Seed content loaded; payments toggled (if applicable); coach SLAs set.
- Delete/export tested; consent text versioned; privacy policy in app.
- App‑only onboarding < 5 min; first workout playable; diet photos render; videos embed.

## 17) Post‑Launch (V1.5 → V2)
- V1.5: more form cues, Apple/Google Health steps, team challenges, referral codes.
- V2: advanced optimizer, deeper RAG, marketplace for templates, richer analytics.

**Next step options:**
1) Generate **FastAPI skeleton** with all routers/services/stubs. 
2) Generate **Next.js screens** (Onboarding, Approvals, Session, Diet, Progress). 
3) Emit **OpenAPI 3.1** for the endpoints above.

